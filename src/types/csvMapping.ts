// CSV field mapping types and interfaces for energy certificate export

export interface CsvFieldMapping {
  csvColumn: string;
  dbPath: string; // Path to the field in the database (e.g., 'objektdaten.PLZ' or 'gebaeudedetails1.Baujahr')
  certificateTypes: ('WG/V' | 'WG/B' | 'NWG/V')[]; // Which certificate types this field applies to
  defaultValue?: string | number;
  transform?: (value: any) => string; // Optional transformation function
}

export interface EnergieausweisData {
  id: string;
  created_at: string;
  updated_at: string;
  user_id: string;
  objektdaten: Record<string, any> | null;
  gebaeudedetails1: Record<string, any> | null;
  gebaeudedetails2: Record<string, any> | null;
  fenster: Record<string, any> | null;
  heizung: Record<string, any> | null;
  trinkwarmwasser: Record<string, any> | null;
  lueftung: Record<string, any> | null;
  verbrauchsdaten: Record<string, any> | null;
  payment_status: string | null;
  certificate_type: string | null;
  stripe_checkout_session_id: string | null;
  order_number: string | null;
}

// Enum value mappings for CSV export
export const csvEnumMappings: Record<string, Record<string, string>> = {
  BedarfVerbrauch: {
    'V': 'V',
    'B': 'B'
  },
  Anlass: {
    'AG_VERMIETUNG': 'AG_VERMIETUNG',
    'AG_AUSHANG': 'AG_AUSHANG', 
    'AG_SONST': 'AG_SONST'
  },
  Datenerhebung: {
    '0': '0',
    '1': '1',
    '2': '2'
  },
  nichtWohnGeb: {
    '0': '0',
    '1': '1'
  },
  isGebaeudehuelle: {
    '0': '0',
    '1': '1'
  },
  anbauSituation: {
    '0': '0',
    '1': '1',
    '2': '2'
  },
  Keller_beheizt: {
    '0': '0',
    '1': '1'
  },
  Klimatisiert: {
    '0': '0',
    '1': '1'
  },
  WSchVo77_erfuellt: {
    '0': '0',
    '1': '1'
  },
  Originaldaemmstandard: {
    '0': '0',
    '1': '1',
    '2': '2'
  },
  Fensterlüftung: {
    '0': '0',
    '1': '1'
  },
  Schachtlüftung: {
    '0': '0',
    '1': '1'
  },
  L_Mit_WRG: {
    '0': '0',
    '1': '1'
  },
  L_Ohne_WRG: {
    '0': '0',
    '1': '1'
  },
  // Energy carrier categories (ETr1, ETr2, ETr3)
  ETr1_Kategorie: {
    'BK_GAS': 'BK_GAS',
    'BK_OEL': 'BK_OEL',
    'BK_STROM': 'BK_STROM',
    'BK_PELLET': 'BK_PELLET',
    'BK_HACKSCHNITZEL': 'BK_HACKSCHNITZEL',
    'BK_STUECKHOLZ': 'BK_STUECKHOLZ',
    'BK_KOHLE': 'BK_KOHLE',
    'BK_BIOGAS': 'BK_BIOGAS',
    'BK_BIOOEL': 'BK_BIOOEL',
    'BK_FW70': 'BK_FW70',
    'BK_FW60': 'BK_FW60',
    'BK_FW50': 'BK_FW50',
    'BK_FW40': 'BK_FW40',
    'BK_FW0': 'BK_FW0',
    'BK_UMWELT': 'BK_UMWELT',
    'BK_NAHW': 'BK_NAHW'
  },
  ETr2_Kategorie: {
    'BK_GAS': 'BK_GAS',
    'BK_OEL': 'BK_OEL',
    'BK_STROM': 'BK_STROM',
    'BK_PELLET': 'BK_PELLET',
    'BK_HACKSCHNITZEL': 'BK_HACKSCHNITZEL',
    'BK_STUECKHOLZ': 'BK_STUECKHOLZ',
    'BK_KOHLE': 'BK_KOHLE',
    'BK_BIOGAS': 'BK_BIOGAS',
    'BK_BIOOEL': 'BK_BIOOEL',
    'BK_FW70': 'BK_FW70',
    'BK_FW60': 'BK_FW60',
    'BK_FW50': 'BK_FW50',
    'BK_FW40': 'BK_FW40',
    'BK_FW0': 'BK_FW0',
    'BK_UMWELT': 'BK_UMWELT',
    'BK_NAHW': 'BK_NAHW'
  },
  ETr3_Kategorie: {
    'BK_GAS': 'BK_GAS',
    'BK_OEL': 'BK_OEL',
    'BK_STROM': 'BK_STROM',
    'BK_PELLET': 'BK_PELLET',
    'BK_HACKSCHNITZEL': 'BK_HACKSCHNITZEL',
    'BK_STUECKHOLZ': 'BK_STUECKHOLZ',
    'BK_KOHLE': 'BK_KOHLE',
    'BK_BIOGAS': 'BK_BIOGAS',
    'BK_BIOOEL': 'BK_BIOOEL',
    'BK_FW70': 'BK_FW70',
    'BK_FW60': 'BK_FW60',
    'BK_FW50': 'BK_FW50',
    'BK_FW40': 'BK_FW40',
    'BK_FW0': 'BK_FW0',
    'BK_UMWELT': 'BK_UMWELT',
    'BK_NAHW': 'BK_NAHW'
  },

  // Energy consumption boolean fields
  ETr1_Heizung: {
    '0': '0',
    '1': '1'
  },
  ETr1_TWW: {
    '0': '0',
    '1': '1'
  },
  ETr1_ZusatzHz: {
    '0': '0',
    '1': '1'
  },
  ETr1_Lueften: {
    '0': '0',
    '1': '1'
  },
  ETr1_Licht: {
    '0': '0',
    '1': '1'
  },
  ETr1_Kuehlen: {
    '0': '0',
    '1': '1'
  },
  ETr1_Sonst: {
    '0': '0',
    '1': '1'
  },
  ETr1_isFw: {
    '0': '0',
    '1': '1'
  },
  ETr1_gebaeudeNahErzeugt: {
    '0': '0',
    '1': '1'
  },

  // ETr2 boolean fields
  ETr2_Heizung: {
    '0': '0',
    '1': '1'
  },
  ETr2_TWW: {
    '0': '0',
    '1': '1'
  },
  ETr2_ZusatzHz: {
    '0': '0',
    '1': '1'
  },
  ETr2_Lueften: {
    '0': '0',
    '1': '1'
  },
  ETr2_Licht: {
    '0': '0',
    '1': '1'
  },
  ETr2_Kuehlen: {
    '0': '0',
    '1': '1'
  },
  ETr2_Sonst: {
    '0': '0',
    '1': '1'
  },
  ETr2_isFw: {
    '0': '0',
    '1': '1'
  },
  ETr2_gebaeudeNahErzeugt: {
    '0': '0',
    '1': '1'
  },

  // ETr3 boolean fields
  ETr3_Heizung: {
    '0': '0',
    '1': '1'
  },
  ETr3_TWW: {
    '0': '0',
    '1': '1'
  },
  ETr3_ZusatzHz: {
    '0': '0',
    '1': '1'
  },
  ETr3_Lueften: {
    '0': '0',
    '1': '1'
  },
  ETr3_Licht: {
    '0': '0',
    '1': '1'
  },
  ETr3_Kuehlen: {
    '0': '0',
    '1': '1'
  },
  ETr3_Sonst: {
    '0': '0',
    '1': '1'
  },
  ETr3_isFw: {
    '0': '0',
    '1': '1'
  },
  ETr3_gebaeudeNahErzeugt: {
    '0': '0',
    '1': '1'
  },

  // Solar and heat pump systems
  TW_Solar: {
    '0': '0',
    '1': '1'
  },
  HZ_Solar: {
    '0': '0',
    '1': '1'
  },
  TW_WP: {
    '0': '0',
    '1': '1'
  },
  HZ_WP: {
    '0': '0',
    '1': '1'
  },

  // Additional building fields
  ergaenzendeErlaeuterungen: {
    '0': '0',
    '1': '1'
  },
  gebaeudeteilAuto: {
    '0': '0',
    '1': '1',
    'false': '0',
    'true': '1'
  },

  // Building component material types
  // Floor materials (Boden_massiv)
  Boden1_massiv: {
    'kb_massiv': 'kb_massiv',
    'kb_Holz': 'kb_Holz',
    'kb_Stahlbeton': 'kb_Stahlbeton'
  },
  Boden2_massiv: {
    'kb_massiv': 'kb_massiv',
    'kb_Holz': 'kb_Holz',
    'kb_Stahlbeton': 'kb_Stahlbeton'
  },

  // Roof materials (Dach_massiv)
  Dach1_massiv: {
    '0': '0', // Holzbalken
    '1': '1'  // Massivdecke
  },
  Dach2_massiv: {
    '0': '0', // Holzbalken
    '1': '1'  // Massivdecke
  },

  // Wall materials (Wand_massiv)
  Wand1_massiv: {
    'kb_massiv': 'kb_massiv',
    'kb_Holz': 'kb_Holz',
    'kb_Stahlbeton': 'kb_Stahlbeton',
    'kb_zweischaligOhneDaemm': 'kb_zweischaligOhneDaemm',
    'kb_zweischaligMitDaemm': 'kb_zweischaligMitDaemm'
  },
  Wand2_massiv: {
    'kb_massiv': 'kb_massiv',
    'kb_Holz': 'kb_Holz',
    'kb_Stahlbeton': 'kb_Stahlbeton',
    'kb_zweischaligOhneDaemm': 'kb_zweischaligOhneDaemm',
    'kb_zweischaligMitDaemm': 'kb_zweischaligMitDaemm'
  },

  // Building component transitions
  Boden1_Kellerdecke: {
    '0': '0',
    '1': '1'
  },
  Boden2_Kellerdecke: {
    '0': '0',
    '1': '1'
  },
  Dach1_Geschossdecke: {
    '0': '0',
    '1': '1'
  },
  Dach2_Geschossdecke: {
    '0': '0',
    '1': '1'
  },

  // Window types (Fenster_Art)
  Fenster1_Art: {
    'fb_HolzEinfach': 'fb_HolzEinfach',
    'fb_HolzIsoliert': 'fb_HolzIsoliert',
    'fb_KunststoffEinfach': 'fb_KunststoffEinfach',
    'fb_KunststoffIsoliert': 'fb_KunststoffIsoliert',
    'fb_KunststoffWSG': 'fb_KunststoffWSG',
    'fb_MetallEinfach': 'fb_MetallEinfach',
    'fb_MetallIsoliert': 'fb_MetallIsoliert',
    'fb_MetallWSG': 'fb_MetallWSG'
  },
  Fenster2_Art: {
    'fb_HolzEinfach': 'fb_HolzEinfach',
    'fb_HolzIsoliert': 'fb_HolzIsoliert',
    'fb_KunststoffEinfach': 'fb_KunststoffEinfach',
    'fb_KunststoffIsoliert': 'fb_KunststoffIsoliert',
    'fb_KunststoffWSG': 'fb_KunststoffWSG',
    'fb_MetallEinfach': 'fb_MetallEinfach',
    'fb_MetallIsoliert': 'fb_MetallIsoliert',
    'fb_MetallWSG': 'fb_MetallWSG'
  },
  Fenster3_Art: {
    'fb_HolzEinfach': 'fb_HolzEinfach',
    'fb_HolzIsoliert': 'fb_HolzIsoliert',
    'fb_KunststoffEinfach': 'fb_KunststoffEinfach',
    'fb_KunststoffIsoliert': 'fb_KunststoffIsoliert',
    'fb_KunststoffWSG': 'fb_KunststoffWSG',
    'fb_MetallEinfach': 'fb_MetallEinfach',
    'fb_MetallIsoliert': 'fb_MetallIsoliert',
    'fb_MetallWSG': 'fb_MetallWSG'
  },

  // Heating system enums
  Hzg_Übergabe: {
    '0': '0', // Heizkörper
    '1': '1'  // Flächenheizung
  },
  Hzg_Verteilung_Art: {
    '0': '0', // Dezentral
    '1': '1', // Gebäudezentral
    '2': '2'  // Wohnungszentral
  },
  Hzg_kreistemperatur: {
    'HKTEMP_90_70': 'HKTEMP_90_70',
    'HKTEMP_70_55': 'HKTEMP_70_55',
    'HKTEMP_55_45': 'HKTEMP_55_45',
    'HKTEMP_35_28': 'HKTEMP_35_28'
  },
  Hzg_Verteilung_Dämmung: {
    '0': '0',
    '1': '1'
  },
  Hzg_Speicher: {
    '0': '0',
    '1': '1'
  },
  Hzg_Aufstellung: {
    'HZ_ZENTRALHEIZUNG': 'HZ_ZENTRALHEIZUNG',
    'HZ_EINZELOFEN': 'HZ_EINZELOFEN',
    'HZ_ETAGENHEIZUNG': 'HZ_ETAGENHEIZUNG',
    'HZ_AUSSERHALB': 'HZ_AUSSERHALB'
  },
  Hzg_Technik: {
    'HZT_STD_KESSEL': 'HZT_STD_KESSEL',
    'HZT_NT_KESSEL': 'HZT_NT_KESSEL',
    'HZT_BW_KESSEL': 'HZT_BW_KESSEL',
    'HZT_KOLLEKTOR': 'HZT_KOLLEKTOR',
    'HZT_BW_GERAET': 'HZT_BW_GERAET',
    'HZT_FERNHZ': 'HZT_FERNHZ',
    'HZT_FC': 'HZT_FC'
  },
  Hzg_Energieträger: {
    'BK_GAS': 'BK_GAS',
    'BK_OEL': 'BK_OEL',
    'BK_STROM': 'BK_STROM',
    'BK_PELLET': 'BK_PELLET',
    'BK_STUECKHOLZ': 'BK_STUECKHOLZ',
    'BK_HACKSCHNITZEL': 'BK_HACKSCHNITZEL',
    'BK_KOHLE': 'BK_KOHLE',
    'BK_FW70': 'BK_FW70',
    'BK_FW60': 'BK_FW60',
    'BK_FW40': 'BK_FW40'
  },

  // Drinking water system enums
  TW_Verteilung_Art: {
    '0': '0', // Dezentral
    '1': '1', // Gebäudezentral
    '2': '2'  // Wohnungszentral
  },
  TW_Verteilung_Dämmung: {
    '0': '0',
    '1': '1'
  },
  TW_Zirkulation: {
    '0': '0',
    '1': '1'
  },
  TW_Speicher_Standort: {
    '1': '1', // Innerhalb
    '2': '2', // Keller
    '3': '3'  // Dach
  },
  TW_Technik: {
    'WT_HZG': 'WT_HZG',
    'WT_EDL': 'WT_EDL',
    'WT_SOLAR': 'WT_SOLAR',
    'WT_WP': 'WT_WP',
    'WT_FERNW': 'WT_FERNW'
  },

  // Ventilation system enums
  Luft_Lage: {
    'GAO_INNERHALB': 'GAO_INNERHALB',
    'GAO_KELLER': 'GAO_KELLER',
    'GAO_DACHRAUM': 'GAO_DACHRAUM'
  },
  Luft_Typ: {
    'LA_FREI': 'LA_FREI',
    'LA_ABL': 'LA_ABL',
    'LA_WRG': 'LA_WRG',
    'LA_WP': 'LA_WP'
  }
};
