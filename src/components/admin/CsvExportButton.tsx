import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '../../lib/supabase';
import { generateCsvData, generateCsvFilename, downloadCsvFile } from '../../utils/csvExport';
import { type EnergieausweisData } from '../../types/csvMapping';

interface CsvExportButtonProps {
  certificateId: string;
  certificateType: string | null;
  orderNumber: string | null;
  className?: string;
}

export const CsvExportButton: React.FC<CsvExportButtonProps> = ({
  certificateId,
  certificateType,
  className = ''
}) => {
  const [isExporting, setIsExporting] = useState(false);
  const [exportError, setExportError] = useState<string | null>(null);

  // Query to fetch complete certificate data when export is triggered
  const { refetch: fetchCertificateData } = useQuery({
    queryKey: ['certificateExport', certificateId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('energieausweise')
        .select('*')
        .eq('id', certificateId)
        .single();

      if (error) {
        throw new Error(`Fehler beim Laden der Zertifikatsdaten: ${error.message}`);
      }

      return data as EnergieausweisData;
    },
    enabled: false, // Only run when manually triggered
  });

  const handleExport = async () => {
    if (!certificateType) {
      setExportError('Zertifikatstyp ist erforderlich für den CSV-Export');
      return;
    }

    // Validate certificate type
    if (!['WG/V', 'WG/B', 'NWG/V'].includes(certificateType)) {
      setExportError(`Ungültiger Zertifikatstyp: ${certificateType}`);
      return;
    }

    setIsExporting(true);
    setExportError(null);

    try {
      // Fetch the complete certificate data
      const { data: certificateData } = await fetchCertificateData();

      if (!certificateData) {
        throw new Error('Zertifikatsdaten konnten nicht geladen werden');
      }

      // Validate that we have the required data
      if (!certificateData.certificate_type) {
        throw new Error('Zertifikatstyp fehlt in den Daten');
      }

      // Generate CSV content
      const csvContent = generateCsvData(certificateData);

      if (!csvContent || csvContent.trim().length === 0) {
        throw new Error('CSV-Inhalt konnte nicht generiert werden');
      }

      // Generate filename
      const filename = generateCsvFilename(certificateData);

      // Download the file
      downloadCsvFile(csvContent, filename);

      // Clear any previous errors on successful export
      setExportError(null);

    } catch (error) {
      console.error('CSV Export Error:', error);
      setExportError(error instanceof Error ? error.message : 'Unbekannter Fehler beim CSV-Export');
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <div className="flex flex-col items-start">
      <button
        onClick={handleExport}
        disabled={isExporting || !certificateType}
        className={`inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed ${className}`}
        title={!certificateType ? 'Zertifikatstyp erforderlich' : 'CSV-Datei herunterladen'}
      >
        {isExporting ? (
          <>
            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Exportiere...
          </>
        ) : (
          <>
            <svg className="-ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            CSV Export
          </>
        )}
      </button>
      
      {exportError && (
        <div className="mt-2 text-sm text-red-600 max-w-xs">
          {exportError}
        </div>
      )}
      
      {!certificateType && (
        <div className="mt-1 text-xs text-gray-500">
          Zertifikatstyp fehlt
        </div>
      )}
    </div>
  );
};

export default CsvExportButton;
